import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/widgets/lo_input_stack_accordion.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/providers/accordion_availability_provider.dart';
import 'package:provider/provider.dart';

/// Demo screen to showcase the Input Stack Accordion component
class InputStackDemoScreen extends StatefulWidget {
  const InputStackDemoScreen({super.key});

  @override
  State<InputStackDemoScreen> createState() => _InputStackDemoScreenState();
}

class _InputStackDemoScreenState extends State<InputStackDemoScreen> {
  final AccordionController _accordionController = AccordionController();

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SelectedObjectProvider()),
        ChangeNotifierProvider(create: (_) => AccordionAvailabilityProvider()),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Input Stack Accordion Demo'),
          backgroundColor: const Color(0xFF0058FF),
          foregroundColor: Colors.white,
        ),
        body: Container(
          color: const Color(0xFFF5F5F5),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Input Stack Accordion Component',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This component demonstrates an accordion-style interface for managing input stack attributes with dropdown functionality, helper text, error messages, and fixed action buttons.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(height: 32),

                // Test buttons to simulate object selection
                Column(
                  children: [
                    // Full object selection buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            provider.addSelectedObject({
                              'name': 'Loan Application',
                              'displayName': 'Loan Application',
                              'entityId': 'loan_app_001',
                            }, [
                              'Customer ID',
                              'Loan Amount',
                              'Interest Rate',
                              'Term Length',
                              'Credit Score'
                            ]);
                          },
                          child: const Text('Add Loan Application'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            provider.addSelectedObject({
                              'name': 'Customer Profile',
                              'displayName': 'Customer Profile',
                              'entityId': 'customer_001',
                            }, [
                              'First Name',
                              'Last Name',
                              'Email',
                              'Phone',
                              'Address'
                            ]);
                          },
                          child: const Text('Add Customer Profile'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            provider.clearAllSelectedObjects();
                          },
                          child: const Text('Clear All'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Individual attribute selection buttons
                    const Text(
                      'Individual Attribute Selection:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            // Simulate individual attribute selection
                            provider.addSelectedObject({
                              'name': 'Loan Application - Customer ID',
                              'displayName': 'Loan Application - Customer ID',
                              'entityId': 'loan_app_001_Customer ID_attr',
                            }, [
                              'Customer ID'
                            ]);
                          },
                          child: const Text('Add Customer ID'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            // Simulate individual attribute selection
                            provider.addSelectedObject({
                              'name': 'Customer Profile - Email',
                              'displayName': 'Customer Profile - Email',
                              'entityId': 'customer_001_Email_attr',
                            }, [
                              'Email'
                            ]);
                          },
                          child: const Text('Add Email'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            final provider =
                                Provider.of<SelectedObjectProvider>(context,
                                    listen: false);
                            // Simulate individual attribute selection
                            provider.addSelectedObject({
                              'name': 'Customer Profile - Phone',
                              'displayName': 'Customer Profile - Phone',
                              'entityId': 'customer_001_Phone_attr',
                            }, [
                              'Phone'
                            ]);
                          },
                          child: const Text('Add Phone'),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Input Stack Accordion Component
                Consumer<SelectedObjectProvider>(
                  builder: (context, selectedObjectProvider, child) {
                    return LoInputStackAccordion(
                      accordionController: _accordionController,
                      title: 'Inputs Stack',
                      attributeCount: selectedObjectProvider
                              .selectedObjectAttributes?.length ??
                          0,
                      // Legacy single object support (for backward compatibility)
                      selectedObject: selectedObjectProvider.selectedObject,
                      selectedObjectAttributes:
                          selectedObjectProvider.selectedObjectAttributes,
                      // New multiple objects support
                      selectedObjects: selectedObjectProvider.selectedObjects,
                      onRemoveObject: (objectId) {
                        selectedObjectProvider.removeSelectedObject(objectId);
                      },
                      availableAttributes: const [
                        '25 Attributes',
                        '20 Attributes',
                        '15 Attributes',
                        '10 Attributes',
                        '5 Attributes',
                      ],
                      isExpanded: true, // Start expanded for demo
                      onAttributeSelected: (value) {
                        print('Attribute selected: $value');
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Selected: $value'),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      onMyLibraryPressed: () {
                        print('My Library pressed');
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('My Library button pressed'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      },
                      onExpansionChanged: (isExpanded) {
                        print('Expansion changed: $isExpanded');
                      },
                    );
                  },
                ),

                const SizedBox(height: 32),

                // Feature highlights
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFFE5E7EB)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Features Implemented:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildFeatureItem(
                          '✅ Accordion expansion/collapse functionality'),
                      _buildFeatureItem(
                          '✅ Custom dropdown widgets for Data Source and UI Control columns'),
                      _buildFeatureItem(
                          '✅ Clickable links in Function Type column'),
                      _buildFeatureItem(
                          '✅ Status indicators with proper styling'),
                      _buildFeatureItem(
                          '✅ Helper text and error message columns'),
                      _buildFeatureItem(
                          '✅ Fixed/sticky Actions column with delete buttons'),
                      _buildFeatureItem(
                          '✅ Horizontal scrollable table content'),
                      _buildFeatureItem('✅ My Library button with add icon'),
                      _buildFeatureItem('✅ Attributes dropdown in header'),
                      _buildFeatureItem('✅ CP toggle switch'),
                      _buildFeatureItem(
                          '✅ Responsive design with proper spacing'),
                      _buildFeatureItem('✅ Consistent theming and typography'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
    );
  }
}
