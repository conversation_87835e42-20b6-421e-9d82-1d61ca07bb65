import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/providers/accordion_availability_provider.dart';
import 'package:provider/provider.dart';

/// Local Objective Input Stack Accordion Component
/// Displays an accordion-style interface for managing input stack attributes
class LoInputStackAccordion extends StatefulWidget {
  final AccordionController? accordionController;
  final String title;
  final int attributeCount;
  final List<String> availableAttributes;
  final Function(String)? onAttributeSelected;
  final VoidCallback? onMyLibraryPressed;
  final bool isExpanded;
  final Function(bool)? onExpansionChanged;

  // Legacy single object support (for backward compatibility)
  final Map<String, dynamic>? selectedObject;
  final List<String>? selectedObjectAttributes;

  // New multiple objects support
  final List<SelectedObjectData>? selectedObjects;
  final Function(String)? onRemoveObject; // Callback to remove an object

  const LoInputStackAccordion({
    super.key,
    this.accordionController,
    this.title = 'Inputs Stack',
    this.attributeCount = 0,
    this.availableAttributes = const [],
    this.onAttributeSelected,
    this.onMyLibraryPressed,
    this.isExpanded = false,
    this.onExpansionChanged,
    this.selectedObject,
    this.selectedObjectAttributes,
    this.selectedObjects,
    this.onRemoveObject,
  });

  @override
  State<LoInputStackAccordion> createState() => _LoInputStackAccordionState();
}

class _LoInputStackAccordionState extends State<LoInputStackAccordion> {
  late AccordionController _internalController;
  bool _isExpanded = false;
  bool _isCPToggleEnabled = false; // Add toggle state

  // Legacy single object data (for backward compatibility)
  late List<Map<String, String>> sampleInputs;

  // Multiple objects data - Map of object ID to its table data
  late Map<String, List<Map<String, String>>> multipleObjectsData;

  // Controllers for editable fields - single object
  List<TextEditingController> helperTextControllers = [];
  List<TextEditingController> errorMessageControllers = [];

  // Controllers for editable fields - multiple objects
  Map<String, List<TextEditingController>> multipleHelperTextControllers = {};
  Map<String, List<TextEditingController>> multipleErrorMessageControllers = {};

  @override
  void initState() {
    super.initState();
    _internalController = widget.accordionController ?? AccordionController();
    _isExpanded = widget.isExpanded;

    // Initialize data structures
    multipleObjectsData = {};
    _initializeSampleInputs();
    _initializeMultipleObjectsData();

    // Report accordion availability after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _reportAccordionAvailability();
    });
  }

  @override
  void dispose() {
    // Dispose all controllers
    _disposeSingleObjectControllers();
    _disposeMultipleObjectControllers();

    // Report accordion unavailability when disposing
    _reportAccordionUnavailability();
    super.dispose();
  }

  /// Report accordion availability to the provider
  void _reportAccordionAvailability() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.markAccordionAvailable(
        isExpanded: _isExpanded,
        screenContext: 'LoInputStackAccordion',
      );
    } catch (e) {
      // Provider might not be available in some contexts (like demo screens)
      // This is acceptable - just continue without reporting
    }
  }

  /// Report accordion unavailability to the provider
  void _reportAccordionUnavailability() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.markAccordionUnavailable();
    } catch (e) {
      // Provider might not be available in some contexts
      // This is acceptable - just continue without reporting
    }
  }

  void _initializeSampleInputs() {
    if (widget.selectedObjectAttributes != null &&
        widget.selectedObjectAttributes!.isNotEmpty) {
      // Populate table with selected object attributes (legacy support)
      sampleInputs = widget.selectedObjectAttributes!
          .map((attribute) => {
                'displayName': attribute,
                'dataSource': '',
                'functionType': '',
                'status': 'required',
                'uiControl': 'String',
                'helperText': 'Enter $attribute',
                'errorMessage': '',
              })
          .toList();
    } else {
      // Show empty table when no object is selected
      sampleInputs = [];
    }

    // Initialize controllers for single object
    _initializeSingleObjectControllers();
  }

  void _initializeSingleObjectControllers() {
    // Dispose existing controllers if any
    _disposeSingleObjectControllers();

    // Initialize new controllers
    helperTextControllers = sampleInputs
        .map((item) => TextEditingController(text: item['helperText']!))
        .toList();
    errorMessageControllers = sampleInputs
        .map((item) => TextEditingController(text: item['errorMessage']!))
        .toList();

    // Add listeners to sync controller changes with data model
    for (int i = 0; i < helperTextControllers.length; i++) {
      helperTextControllers[i].addListener(() {
        if (i < sampleInputs.length) {
          sampleInputs[i]['helperText'] = helperTextControllers[i].text;
        }
      });
    }

    for (int i = 0; i < errorMessageControllers.length; i++) {
      errorMessageControllers[i].addListener(() {
        if (i < sampleInputs.length) {
          sampleInputs[i]['errorMessage'] = errorMessageControllers[i].text;
        }
      });
    }
  }

  void _disposeSingleObjectControllers() {
    if (helperTextControllers.isNotEmpty) {
      for (var controller in helperTextControllers) {
        controller.dispose();
      }
      helperTextControllers.clear();
    }
    if (errorMessageControllers.isNotEmpty) {
      for (var controller in errorMessageControllers) {
        controller.dispose();
      }
      errorMessageControllers.clear();
    }
  }

  void _initializeMultipleObjectsData() {
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      multipleObjectsData.clear();
      for (final objectData in widget.selectedObjects!) {
        multipleObjectsData[objectData.id] = objectData.attributes
            .map((attribute) => {
                  'displayName': attribute,
                  'dataSource': '',
                  'functionType': '',
                  'status': 'required',
                  'uiControl': 'String',
                  'helperText': 'Enter $attribute',
                  'errorMessage': '',
                })
            .toList();
      }
      _initializeMultipleObjectControllers();
    }
  }

  void _initializeMultipleObjectControllers() {
    // Dispose existing controllers
    _disposeMultipleObjectControllers();

    // Initialize controllers for each object
    for (final objectData in widget.selectedObjects!) {
      final objectTableData = multipleObjectsData[objectData.id] ?? [];

      multipleHelperTextControllers[objectData.id] = objectTableData
          .map((item) => TextEditingController(text: item['helperText']!))
          .toList();

      multipleErrorMessageControllers[objectData.id] = objectTableData
          .map((item) => TextEditingController(text: item['errorMessage']!))
          .toList();

      // Add listeners to sync controller changes with data model
      final helperControllers = multipleHelperTextControllers[objectData.id]!;
      final errorControllers = multipleErrorMessageControllers[objectData.id]!;

      for (int i = 0; i < helperControllers.length; i++) {
        helperControllers[i].addListener(() {
          if (i < objectTableData.length) {
            multipleObjectsData[objectData.id]![i]['helperText'] =
                helperControllers[i].text;
          }
        });
      }

      for (int i = 0; i < errorControllers.length; i++) {
        errorControllers[i].addListener(() {
          if (i < objectTableData.length) {
            multipleObjectsData[objectData.id]![i]['errorMessage'] =
                errorControllers[i].text;
          }
        });
      }
    }
  }

  void _disposeMultipleObjectControllers() {
    for (var controllerList in multipleHelperTextControllers.values) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    multipleHelperTextControllers.clear();

    for (var controllerList in multipleErrorMessageControllers.values) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    multipleErrorMessageControllers.clear();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    widget.onExpansionChanged?.call(_isExpanded);

    if (widget.accordionController != null) {
      widget.accordionController!.togglePanel('lo_input_stack');
    }

    // Report expansion state change
    _reportExpansionChange();
  }

  /// Report expansion state change to the provider
  void _reportExpansionChange() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.updateAccordionExpansion(_isExpanded);
    } catch (e) {
      // Provider might not be available in some contexts
      // This is acceptable - just continue without reporting
    }
  }

  String _getDisplayTitle() {
    // For multiple objects, show count
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return '${widget.title} (${widget.selectedObjects!.length} objects)';
    }

    // Legacy single object support
    if (widget.selectedObject != null &&
        widget.selectedObject!['displayName'] != null) {
      return widget.selectedObject!['displayName'];
    } else if (widget.selectedObject != null &&
        widget.selectedObject!['name'] != null) {
      return widget.selectedObject!['name'];
    }
    return widget.title;
  }

  String _getExpandedContentTitle() {
    // For multiple objects, show generic title
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return 'Input Stack Configuration (${widget.selectedObjects!.length} objects)';
    }

    // Legacy single object support
    if (widget.selectedObject != null &&
        widget.selectedObject!['displayName'] != null) {
      return '${widget.selectedObject!['displayName']}';
    } else if (widget.selectedObject != null &&
        widget.selectedObject!['name'] != null) {
      return '${widget.selectedObject!['name']}';
    }
    return 'Input Stack Configuration';
  }

  int _getDynamicAttributeCount() {
    // For multiple objects, sum all attributes
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return widget.selectedObjects!
          .fold(0, (sum, obj) => sum + obj.attributes.length);
    }

    // Legacy single object support
    if (widget.selectedObjectAttributes != null) {
      return widget.selectedObjectAttributes!.length;
    }
    return widget.attributeCount;
  }

  @override
  void didUpdateWidget(LoInputStackAccordion oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Re-initialize sample inputs if selected object or attributes changed (legacy support)
    if (oldWidget.selectedObject != widget.selectedObject ||
        oldWidget.selectedObjectAttributes != widget.selectedObjectAttributes) {
      _initializeSampleInputs();
    }

    // Re-initialize multiple objects data if selectedObjects changed
    if (oldWidget.selectedObjects != widget.selectedObjects) {
      _initializeMultipleObjectsData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isExpanded =
        widget.accordionController?.isPanelExpanded('lo_input_stack') ??
            _isExpanded;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Header Row
          InkWell(
            onTap: _toggleExpansion,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  // Title
                  Expanded(
                    child: Text(
                      widget.title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight:
                            isExpanded ? FontWeight.w500 : FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  // My Library Button
                  _buildMyLibraryButton(context),

                  const SizedBox(width: 12),

                  // Attributes Count Display
                  Text(
                    '${_getDynamicAttributeCount()} Attributes',
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),

                  // // CP Toggle Switch
                  // _buildCPToggle(context),

                  const SizedBox(width: 12),

                  // Expansion Arrow
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Expanded Content
          if (isExpanded)
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildExpandedContent(context),
            ),
        ],
      ),
    );
  }

  Widget _buildMyLibraryButton(BuildContext context) {
    return InkWell(
      onTap: widget.onMyLibraryPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF0058FF),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.add,
              color: Colors.white,
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              'My Library',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCPToggle(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Radio button (toggle switch style)
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _isCPToggleEnabled = !_isCPToggleEnabled;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 32,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: _isCPToggleEnabled
                    ? const Color(0xFF0058FF) // Blue for active state
                    : const Color(0xFF9CA3AF), // Gray for inactive state
              ),
              child: Stack(
                children: [
                  // Toggle circle with animation
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInOut,
                    left:
                        _isCPToggleEnabled ? 18 : 2, // Move right when enabled
                    top: 2,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        // CP label
        Text(
          'CP',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // Widget _buildAttributesDropdown(BuildContext context) {
  //   return SizedBox(
  //     width: 120,
  //     child: CustomDropdownWidget(
  //       label: '${widget.attributeCount} Attributes',
  //       list: widget.availableAttributes.isNotEmpty
  //           ? widget.availableAttributes
  //           : _getDefaultAttributes(),
  //       value: '${widget.attributeCount} Attributes',
  //       onChanged: (value) {
  //         widget.onAttributeSelected?.call(value);
  //       },
  //     ),
  //   );
  // }

  List<String> _getDefaultAttributes() {
    return [
      '25 Attributes',
      '20 Attributes',
      '15 Attributes',
      '10 Attributes',
      '5 Attributes',
    ];
  }

  /// Show confirmation dialog before deleting a row
  void _showDeleteConfirmationDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          title: Text(
            'Delete Row',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${sampleInputs[index]['displayName']}"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteRow(index);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Delete a row from the table
  void _deleteRow(int index) {
    setState(() {
      sampleInputs.removeAt(index);
    });

    // Show a snackbar to confirm deletion
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Row deleted successfully',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Show confirmation dialog before deleting a row from a specific object
  void _showDeleteConfirmationDialogForObject(
      BuildContext context, String objectId, int index) {
    final objectTableData = multipleObjectsData[objectId] ?? [];
    if (index >= objectTableData.length) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          title: Text(
            'Delete Row',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${objectTableData[index]['displayName']}"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteRowFromObject(objectId, index);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Delete a row from a specific object's table
  void _deleteRowFromObject(String objectId, int index) {
    setState(() {
      multipleObjectsData[objectId]?.removeAt(index);
    });

    // Show a snackbar to confirm deletion
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Row deleted successfully',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getExpandedContentTitle(),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              // CP Radio Toggle
              _buildCPToggle(context)
            ],
          ),
          const SizedBox(height: 16),

          // Multiple tables or single table
          _buildTablesContent(context),
        ],
      ),
    );
  }

  Widget _buildTablesContent(BuildContext context) {
    // Check if we have multiple objects
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return _buildMultipleTables(context);
    }

    // Fall back to legacy single table
    return _buildInputStackTable(context);
  }

  Widget _buildMultipleTables(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.selectedObjects!.map((objectData) {
        return Container(
          margin: const EdgeInsets.only(bottom: 24), // Space between tables
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Object title row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    objectData.object['displayName'] ??
                        objectData.object['name'] ??
                        'Unknown Object',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  // Remove button for this object
                  if (widget.onRemoveObject != null)
                    InkWell(
                      onTap: () => widget.onRemoveObject!(objectData.id),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.red,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              // Table for this object
              _buildObjectTable(context, objectData),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildObjectTable(
      BuildContext context, SelectedObjectData objectData) {
    final objectTableData = multipleObjectsData[objectData.id] ?? [];

    // Dropdown options
    final dataSourceOptions = [
      'User',
      'Nested Function',
      'Mapping',
      'Constant',
      'Condition Potential',
    ];

    final uiControlOptions = [
      'String',
      'Alpha Numerical',
      'Dropdown',
      'Text',
      'Number',
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // Scrollable content (unified scrolling)
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicWidth(
                child: Column(
                  children: [
                    // Header
                    Container(
                      height: 40,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: const BoxDecoration(
                        color: Color(0xFFF9FAFB),
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(4)),
                      ),
                      child: Row(
                        children: [
                          _buildHeaderCell(context, 'DISPLAY NAME', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'DATA SOURCE', 160),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'FUNCTION TYPE', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'STATUS', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'UI CONTROL', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'HELPER TEXT', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'ERROR MESSAGE', 140),
                        ],
                      ),
                    ),
                    // Rows
                    if (objectTableData.isEmpty)
                      Container(
                        height: 100,
                        padding: const EdgeInsets.all(16),
                        child: Center(
                          child: Text(
                            'No attributes found for this object.',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    else
                      ...objectTableData.asMap().entries.map((entry) {
                        final index = entry.key;
                        final input = entry.value;
                        return Container(
                          height: 50, // Fixed height to match design
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color:
                                Colors.white, // All rows white - no alternating
                            border: Border(
                              bottom: BorderSide(
                                color: index < objectTableData.length - 1
                                    ? const Color(0xFFE5E7EB)
                                    : Colors.transparent,
                              ),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildDataCell(
                                  context, input['displayName']!, 140),
                              const SizedBox(width: 24),
                              _buildDataSourceDropdownCell(
                                  context,
                                  input['dataSource']!,
                                  dataSourceOptions,
                                  160, (value) {
                                setState(() {
                                  multipleObjectsData[objectData.id]![index]
                                      ['dataSource'] = value;
                                  // Update function type based on data source selection
                                  multipleObjectsData[objectData.id]![index]
                                          ['functionType'] =
                                      _getDefaultFunctionTypeForDataSource(
                                          value);
                                });
                              }),
                              const SizedBox(width: 24),
                              _buildDynamicFunctionTypeCell(
                                  context,
                                  input['functionType']!,
                                  input['dataSource']!,
                                  120,
                                  index),
                              const SizedBox(width: 24),
                              _buildStatusCell(
                                  context, input['status']!, 120, index,
                                  objectId: objectData.id),
                              const SizedBox(width: 24),
                              _buildDropdownCell(context, input['uiControl']!,
                                  uiControlOptions, 120, (value) {
                                setState(() {
                                  multipleObjectsData[objectData.id]![index]
                                      ['uiControl'] = value;
                                });
                              }),
                              const SizedBox(width: 24),
                              _buildEditableCellForObject(
                                  context, objectData.id, index, true, 140),
                              const SizedBox(width: 24),
                              _buildEditableCellForObject(
                                  context, objectData.id, index, false, 140),
                            ],
                          ),
                        );
                      }),
                  ],
                ),
              ),
            ),
          ),
          // Fixed/Sticky Actions column
          Container(
            width: 80,
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB)),
              ),
            ),
            child: Column(
              children: [
                // Actions Header
                Container(
                  height: 40, // Match table header height exactly
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF9FAFB),
                    borderRadius:
                        BorderRadius.only(topRight: Radius.circular(4)),
                  ),
                  child: Center(
                    child: Text(
                      'ACTIONS',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                // Action buttons for each row or empty state
                if (objectTableData.isEmpty)
                  Container(
                    height: 100,
                    child: const SizedBox.shrink(),
                  )
                else
                  ...objectTableData.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Container(
                      height: 50, // Match table row height exactly
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 0),
                      decoration: BoxDecoration(
                        color: Colors.white, // All white to match table rows
                        border: Border(
                          bottom: BorderSide(
                            color: index < objectTableData.length - 1
                                ? const Color(0xFFE5E7EB)
                                : Colors.transparent,
                          ),
                        ),
                      ),
                      child: Center(
                        child: InkWell(
                          onTap: () {
                            _showDeleteConfirmationDialogForObject(
                                context, objectData.id, index);
                          },
                          borderRadius: BorderRadius.circular(2),
                          child: Container(
                              child: const Icon(
                            Icons.delete_outline,
                            size: 16,
                            color: Colors.red,
                          )),
                        ),
                      ),
                    );
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputStackTable(BuildContext context) {
    // Dropdown options
    final dataSourceOptions = [
      'User',
      'Nested Function',
      'Mapping',
      'Constant',
      'Conditional Potential',
    ];

    final uiControlOptions = [
      'String',
      'Alpha Numerical',
      'Dropdown',
      'Text',
      'Number',
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // Scrollable content (unified scrolling)
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicWidth(
                child: Column(
                  children: [
                    // Header
                    Container(
                      height: 40,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: const BoxDecoration(
                        color: Color(0xFFF9FAFB),
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(4)),
                      ),
                      child: Row(
                        children: [
                          _buildHeaderCell(context, 'DISPLAY NAME', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'DATA SOURCE', 160),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'FUNCTION TYPE', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'STATUS', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'UI CONTROL', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'HELPER TEXT', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'ERROR MESSAGE', 140),
                        ],
                      ),
                    ),
                    // Rows or Empty State
                    if (sampleInputs.isEmpty)
                      Container(
                        height: 100,
                        padding: const EdgeInsets.all(16),
                        child: Center(
                          child: Text(
                            'No object selected. Click the + button next to an object to populate this table.',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    else
                      ...sampleInputs.asMap().entries.map((entry) {
                        final index = entry.key;
                        final input = entry.value;
                        return Container(
                          height: 50, // Fixed height to match design
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color:
                                Colors.white, // All rows white - no alternating
                            border: Border(
                              bottom: BorderSide(
                                color: index < sampleInputs.length - 1
                                    ? const Color(0xFFE5E7EB)
                                    : Colors.transparent,
                              ),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildDataCell(
                                  context, input['displayName']!, 140),
                              const SizedBox(width: 24),
                              _buildDataSourceDropdownCell(
                                  context,
                                  input['dataSource']!,
                                  dataSourceOptions,
                                  160, (value) {
                                setState(() {
                                  sampleInputs[index]['dataSource'] = value;
                                  // Update function type based on data source selection
                                  sampleInputs[index]['functionType'] =
                                      _getDefaultFunctionTypeForDataSource(
                                          value);
                                });
                              }),
                              const SizedBox(width: 24),
                              _buildDynamicFunctionTypeCell(
                                  context,
                                  input['functionType']!,
                                  input['dataSource']!,
                                  120,
                                  index),
                              const SizedBox(width: 24),
                              _buildStatusCell(
                                  context, input['status']!, 120, index),
                              const SizedBox(width: 24),
                              _buildDropdownCell(context, input['uiControl']!,
                                  uiControlOptions, 120, (value) {
                                setState(() {
                                  sampleInputs[index]['uiControl'] = value;
                                });
                              }),
                              const SizedBox(width: 24),
                              _buildEditableCell(context, index, true, 140),
                              const SizedBox(width: 24),
                              _buildEditableCell(context, index, false, 140),
                            ],
                          ),
                        );
                      }),
                  ],
                ),
              ),
            ),
          ),
          // Fixed/Sticky Actions column
          Container(
            width: 80,
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB)),
              ),
            ),
            child: Column(
              children: [
                // Actions Header
                Container(
                  height: 40, // Match table header height exactly
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF9FAFB),
                    borderRadius:
                        BorderRadius.only(topRight: Radius.circular(4)),
                  ),
                  child: Center(
                    child: Text(
                      'ACTIONS',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                // Action buttons for each row or empty state
                if (sampleInputs.isEmpty)
                  Container(
                    height: 100,
                    child: const SizedBox.shrink(),
                  )
                else
                  ...sampleInputs.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Container(
                      height: 50, // Match table row height exactly
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 0),
                      decoration: BoxDecoration(
                        color: Colors.white, // All white to match table rows
                        border: Border(
                          bottom: BorderSide(
                            color: index < sampleInputs.length - 1
                                ? const Color(0xFFE5E7EB)
                                : Colors.transparent,
                          ),
                        ),
                      ),
                      child: Center(
                        child: InkWell(
                          onTap: () {
                            _showDeleteConfirmationDialog(context, index);
                          },
                          borderRadius: BorderRadius.circular(2),
                          child: Container(
                              child: const Icon(
                            Icons.delete_outline,
                            size: 16,
                            color: Colors.red,
                          )),
                        ),
                      ),
                    );
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String title, double width) {
    return SizedBox(
      width: width,
      child: Text(
        title,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w600,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildDataCell(BuildContext context, String data, double width) {
    return SizedBox(
      width: width,
      child: Text(
        data,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildStatusCell(
      BuildContext context, String status, double width, int index,
      {String? objectId}) {
    final statusOptions = [
      'required',
      'Required',
      'Optional',
    ];

    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: status.isEmpty ? 'Select...' : status,
        list: statusOptions,
        value: status.isEmpty ? null : status,
        onChanged: (value) {
          setState(() {
            if (objectId != null) {
              // Update for multiple objects table
              multipleObjectsData[objectId]![index]['status'] = value;
            } else {
              // Update for single object table
              sampleInputs[index]['status'] = value;
            }
          });
        },
      ),
    );
  }

  Widget _buildErrorCell(BuildContext context, String error, double width) {
    return SizedBox(
      width: width,
      child: Text(
        error,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: error.isNotEmpty ? const Color(0xFFFF4444) : Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildEditableCell(
      BuildContext context, int index, bool isHelperText, double width) {
    final controller = isHelperText
        ? (index < helperTextControllers.length
            ? helperTextControllers[index]
            : null)
        : (index < errorMessageControllers.length
            ? errorMessageControllers[index]
            : null);

    // If controller is null, return a static text cell as fallback
    if (controller == null) {
      return _buildDataCell(context, '', width);
    }

    return SizedBox(
      width: width,
      child: Container(
        height: 35, // Match the CustomDropdownWidget height
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: TextField(
            controller: controller,
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              filled: false,
              isDense: true,
            ),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditableCellForObject(BuildContext context, String objectId,
      int index, bool isHelperText, double width) {
    final controllers = isHelperText
        ? multipleHelperTextControllers[objectId]
        : multipleErrorMessageControllers[objectId];

    final controller = (controllers != null && index < controllers.length)
        ? controllers[index]
        : null;

    // If controller is null, return a static text cell as fallback
    if (controller == null) {
      return _buildDataCell(context, '', width);
    }

    return SizedBox(
      width: width,
      child: Container(
        height: 35, // Match the CustomDropdownWidget height
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: TextField(
            controller: controller,
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              filled: false,
              isDense: true,
            ),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownCell(BuildContext context, String currentValue,
      List<String> options, double width, Function(String) onChanged) {
    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: currentValue.isEmpty ? 'Select...' : currentValue,
        list: options,
        value: currentValue.isEmpty ? null : currentValue,
        onChanged: (value) {
          onChanged(value);
        },
      ),
    );
  }

  Widget _buildDataSourceDropdownCell(BuildContext context, String currentValue,
      List<String> options, double width, Function(String) onChanged) {
    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: currentValue.isEmpty ? 'Select Data Source' : currentValue,
        list: options,
        value: currentValue.isEmpty ? null : currentValue,
        onChanged: (value) {
          onChanged(value);
        },
      ),
    );
  }

  Widget _buildDynamicFunctionTypeCell(BuildContext context,
      String functionType, String dataSource, double width, int index) {
    return SizedBox(
      width: width,
      child: Row(
        children: [
          // Icon based on data source
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: _getFunctionTypeIconByDataSource(dataSource),
          ),
          // Function type text/link based on data source
          Expanded(
            child: _buildFunctionTypeContent(
                context, functionType, dataSource, index),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionTypeCell(
      BuildContext context, String functionType, double width) {
    return SizedBox(
      width: width,
      child: Row(
        children: [
          // Icon based on data source
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: _getFunctionTypeIcon(functionType),
          ),
          // Function type text/link based on data source
          Expanded(
            child: Text(
              functionType,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Show unified modal with dynamic content based on selection
  void _showUnifiedModal(BuildContext context, String functionType) {
    print('Showing modal for function type: $functionType'); // Debug print
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _UnifiedModal(
          initialFunctionType: functionType,
        );
      },
    );
  }

  Widget _getFunctionTypeIcon(String functionType) {
    switch (functionType.toLowerCase()) {
      case 'send email':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'nested function':
      case 'lo.mig_entity.attribute':
      case 'address 11':
      case 'entity.attribute':
        return SvgPicture.asset(
          'assets/images/my_library/user.svg',
          width: 16,
          height: 16,
        );
      default:
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
    }
  }

  Widget _getFunctionTypeIconByDataSource(String dataSource) {
    // Handle empty data source
    if (dataSource.isEmpty) {
      return SvgPicture.asset(
        'assets/images/my_library/assistant.svg',
        width: 16,
        height: 16,
      );
    }

    switch (dataSource.toLowerCase()) {
      case 'user':
        return SvgPicture.asset(
          'assets/images/my_library/user.svg',
          width: 16,
          height: 16,
        );
      case 'nested function':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'mapping':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'constant':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'conditional potential':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      default:
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
    }
  }

  Widget _buildFunctionTypeContent(
      BuildContext context, String functionType, String dataSource, int index) {
    // For empty/unselected data source, show "N/A" without any dialog functionality
    if (dataSource.isEmpty) {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // For "User" data source, show "N/A" without any dialog functionality
    if (dataSource.toLowerCase() == 'user') {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // For all other data sources, show clickable element that opens respective dialogs
    if (functionType.isNotEmpty) {
      return InkWell(
        onTap: () {
          _showDialogBasedOnDataSource(context, dataSource, functionType);
        },
        child: Text(
          functionType,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: const Color(0xFF0058FF), // Blue color for links
            decoration: TextDecoration.underline,
          ).copyWith(
            decorationColor: const Color(0xFF0058FF),
          ),
          overflow: TextOverflow.ellipsis,
        ),
      );
    } else {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  void _showDialogBasedOnDataSource(
      BuildContext context, String dataSource, String functionType) {
    switch (dataSource.toLowerCase()) {
      case 'nested function':
        _showUnifiedModal(context, 'Nested Function');
        break;
      case 'mapping':
        _showUnifiedModal(context, 'LO.mig_Entity.Attribute');
        break;
      case 'constant':
        _showUnifiedModal(context, 'Address 11');
        break;
      case 'conditional potential':
        _showUnifiedModal(context, 'Entity.Attribute');
        break;
      default:
        // For any other case, use the existing function type
        _showUnifiedModal(context, functionType);
        break;
    }
  }

  String _getDefaultFunctionTypeForDataSource(String dataSource) {
    switch (dataSource.toLowerCase()) {
      case 'user':
        return ''; // Empty for N/A display
      case 'nested function':
        return 'Send email';
      case 'mapping':
        return 'LO.mig_Entity.Attribute';
      case 'constant':
        return 'Address 11';
      case 'conditional potential':
        return 'Entity.Attribute';
      default:
        return '';
    }
  }

  Widget _buildLinkCell(BuildContext context, String data, double width) {
    return SizedBox(
      width: width,
      child: data.isNotEmpty
          ? InkWell(
              onTap: () {
                // Handle link click - could navigate or show details
                print('Link clicked: $data');
              },
              child: Text(
                data,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: const Color(0xFF0058FF), // Blue color for links
                  decoration: TextDecoration.underline,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            )
          : Text(
              data,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black87,
              ),
              overflow: TextOverflow.ellipsis,
            ),
    );
  }

  /// Build custom tooltip with enhanced styling and positioning
  Widget _buildCustomTooltip({
    required String message,
    required Widget child,
  }) {
    return Tooltip(
      message: message,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      textStyle: FontManager.getCustomStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w400,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      margin: const EdgeInsets.all(4),
      preferBelow: true,
      verticalOffset: 8,
      waitDuration: const Duration(milliseconds: 500),
      showDuration: const Duration(seconds: 3),
      child: child,
    );
  }
}

/// Unified Modal Widget that changes content based on dropdown selection
class _UnifiedModal extends StatefulWidget {
  final String initialFunctionType;

  const _UnifiedModal({
    required this.initialFunctionType,
  });

  @override
  State<_UnifiedModal> createState() => _UnifiedModalState();
}

class _UnifiedModalState extends State<_UnifiedModal> {
  late String selectedModalType;

  // Controllers for different modal types
  final Map<String, Map<String, TextEditingController>> _controllers = {};

  @override
  void initState() {
    super.initState();
    selectedModalType =
        _getModalTypeFromFunctionType(widget.initialFunctionType);
    _initializeControllers();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (var controllerMap in _controllers.values) {
      for (var controller in controllerMap.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  String _getModalTypeFromFunctionType(String functionType) {
    print('Function type received: "$functionType"'); // Debug print

    // Map function types to their corresponding modal types
    switch (functionType.toLowerCase()) {
      case 'lo.mig_entity.attribute':
        return 'Add Data Mapping';
      case 'address 11':
        return 'Select Entity.attribute';
      case 'entity.attribute':
        return 'Condition Potentiality';
      case 'condition potential':
        return 'Condition Potentiality';
      case 'nested function':
      case 'send email':
      default:
        return 'Nested Function Configuration';
    }
  }

  void _initializeControllers() {
    // Initialize controllers for all modal types
    _controllers['Nested Function Configuration'] = {
      'attributeName': TextEditingController(),
      'functionType': TextEditingController(text: 'conditional_assignment'),
      'functionName': TextEditingController(),
      'functionInputs': TextEditingController(),
      'condition': TextEditingController(),
    };

    _controllers['Add Data Mapping'] = {
      'loName': TextEditingController(),
      'sourceLO1': TextEditingController(),
      'sourceLO2': TextEditingController(),
      'sourceEntityAttribute1': TextEditingController(),
      'sourceEntityAttribute2': TextEditingController(),
    };

    _controllers['Select Entity.attribute'] = {
      'sourceEntityAttribute': TextEditingController(),
      'condition': TextEditingController(),
    };

    _controllers['Condition Potentiality'] = {
      'sourceEntity': TextEditingController(),
      'sourceEntityAttribute': TextEditingController(),
    };
  }

  @override
  Widget build(BuildContext context) {
    print('Building modal with type: $selectedModalType'); // Debug print

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 24,
      child: Container(
        width: _getModalWidth(),
        height: _getModalHeight(),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 24,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with drop shadow
            Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedModalType,
                    style: FontManager.getCustomStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, size: 20),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // Dynamic content based on selected modal type
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: SingleChildScrollView(
                  child: _buildModalContent(),
                ),
              ),
            ),

            // Footer buttons with drop shadow
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!, width: 1),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isFormValid()
                        ? () {
                            // Handle save/apply action
                            Navigator.of(context).pop();
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFormValid()
                          ? const Color(0xFF0058FF)
                          : Colors.grey[300],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      _getButtonText(),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _getModalWidth() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 550;
      case 'Select Entity.attribute':
        return 500;
      case 'Condition Potentiality':
        return 550;
      default:
        return 650;
    }
  }

  double _getModalHeight() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 480;
      case 'Select Entity.attribute':
        return 480;
      case 'Condition Potentiality':
        return 350;
      default:
        return 486;
    }
  }

  String _getButtonText() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 'Apply This';
      case 'Select Entity.attribute':
        return 'Apply This';
      case 'Condition Potentiality':
        return 'Apply This';
      default:
        return 'Save Configuration';
    }
  }

  bool _isFormValid() {
    final controllers = _controllers[selectedModalType];
    if (controllers == null) return true; // Default to enabled

    switch (selectedModalType) {
      case 'Nested Function Configuration':
        // Since we pre-populate the fields, the form should be valid by default
        return true;
      case 'Add Data Mapping':
        return true; // Always valid for demo
      case 'Select Entity.attribute':
        // Enable if any value is selected (including pre-populated ones)
        return true;
      case 'Condition Potentiality':
        // Enable if any value is selected (including pre-populated ones)
        return true;
      default:
        return true;
    }
  }

  Widget _buildModalContent() {
    switch (selectedModalType) {
      case 'Nested Function Configuration':
        return _buildNestedFunctionContent();
      case 'Add Data Mapping':
        return _buildDataMappingContent();
      case 'Select Entity.attribute':
        return _buildSelectEntityContent();
      case 'Condition Potentiality':
        return _buildConditionPotentialityContent();
      default:
        return _buildNestedFunctionContent();
    }
  }

  Widget _buildNestedFunctionContent() {
    final controllers = _controllers['Nested Function Configuration']!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Attribute name section
        Text(
          'Attribute name',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelLarge(context),
            fontWeight: FontWeight.w600,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 20),

        // Function Type and Function Name side by side
        Row(
          children: [
            // Function Type with info icon (left side)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Function Type',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Tooltip(
                        message:
                            'Select function operation (e.g., create_record, update_record, fetch_records)',
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8F5E8),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                              color: const Color(0xFFB8E6B8), width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        textStyle: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black87,
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        margin: const EdgeInsets.all(2),
                        preferBelow: true,
                        verticalOffset: 8,
                        waitDuration: const Duration(milliseconds: 300),
                        showDuration: const Duration(seconds: 4),
                        child: Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Color(0xFF779FED),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select Function Type',
                    list: [
                      'conditional_assignment',
                      'send_email',
                      'validate_data',
                      'create_record',
                      'update_record'
                    ],
                    value: controllers['functionType']!.text.isEmpty
                        ? null
                        : controllers['functionType']!.text,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        controllers['functionType']!.text =
                            selectedValue as String;
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 24),
            // Function Name - just text display
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Function Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 13),
                  Text(
                    'Attribute name: Function Type',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Function Inputs section
        Text(
          'Function Inputs',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleLarge(context),
            fontWeight: FontWeight.w600,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        // Input Attributes label and dropdown in a row
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Input Attributes',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select Input Attributes',
                    list: [
                      'conditional_assignment',
                      'send_email',
                      'validate_data',
                      'create_record'
                    ],
                    value: controllers['functionInputs']!.text.isEmpty
                        ? null
                        : controllers['functionInputs']!.text,
                    onChanged: (dynamic value) {
                      setState(() {
                        controllers['functionInputs']!.text = value as String;
                      });
                    },
                  ),
                ],
              ),
            ),
            const Expanded(
                flex: 1, child: SizedBox()), // Empty space on the right
          ],
        ),
        // Helper text
        Text(
          'Specify input entities and attributes (use * for required)',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 24),

        // Condition section
        Text(
          'Condition',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelLarge(context),
            fontWeight: FontWeight.w600,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),

        // Condition text input with 34px height
        Container(
          height: 34,
          child: TextField(
            controller: controllers['condition']!,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFFCECECE), width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFFCECECE), width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding: const EdgeInsets.only(
                  left: 12, bottom: 12, top: 12, right: 12),
              filled: false,
              isDense: true,
              hintText: 'Enter condition',
              hintStyle: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            onChanged: (value) => setState(() {}),
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildDataMappingContent() {
    final controllers = _controllers['Add Data Mapping']!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 26),
        Text(
          'LO name - Entity - Attribute Name',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.bold,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 19),

        // Select Source LO 1 and Select Source Entity Attribute
        Row(
          children: [
            Expanded(
              child: _buildCustomDropdownField(
                'Select Source LO 1',
                'Cash',
                ['Cash', 'Credit', 'Debit'],
                (value) {
                  setState(() {
                    controllers['sourceLO1']!.text = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCustomDropdownField(
                'Select Source Entity.Attribute',
                'Select Entity.Attribute',
                ['Select Entity.Attribute', 'Customer ID', 'Email', 'Name'],
                (value) {
                  setState(() {
                    controllers['sourceEntityAttribute1']!.text = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),

        // Source LO 2
        Text(
          'Source LO 2',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildCustomDropdownField(
                'Source Entity.Attribute',
                'Card',
                ['Card', 'Bank Transfer', 'Wallet'],
                (value) {
                  setState(() {
                    controllers['sourceLO2']!.text = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCustomDropdownField(
                'Entity.Attribute',
                'Entity.Attribute',
                ['Entity.Attribute', 'Transaction ID', 'Amount', 'Date'],
                (value) {
                  setState(() {
                    controllers['sourceEntityAttribute2']!.text = value;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        Text(
          'Attribute LO is not option to map. Depends on your requirement',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Color(0xfffF0000),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSelectEntityContent() {
    final controllers = _controllers['Select Entity.attribute']!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Source Entity.Attribute section
        Text(
          'Source Entity.Attribute',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Source Entity.Attribute dropdown with half width
        Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomDropdownWidget(
                label: 'Select',
                list: [
                  'Select',
                  'Customer.ID',
                  'Customer.Name',
                  'Customer.Email',
                  'Customer.Phone',
                  'Address.Street',
                  'Address.City',
                  'Address.State',
                  'Address.ZipCode',
                  'Address.Country',
                  'Address11.Line1',
                  'Address11.Line2',
                  'Address11.City',
                  'Address11.State',
                  'Address11.ZipCode',
                  'Order.ID',
                  'Order.Date',
                  'Order.Amount',
                  'Product.ID',
                  'Product.Name',
                ],
                value: controllers['sourceEntityAttribute']!.text.isEmpty
                    ? 'Select'
                    : controllers['sourceEntityAttribute']!.text,
                onChanged: (dynamic selectedValue) {
                  setState(() {
                    controllers['sourceEntityAttribute']!.text =
                        selectedValue as String;
                  });
                },
              ),
            ),
            const Expanded(
              flex: 1,
              child: SizedBox(), // Empty space to make dropdown half width
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Condition section
        Text(
          'Condition',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Condition input field
        Container(
          height: 40,
          child: TextField(
            controller: controllers['condition']!..text = 'Create',
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              filled: true,
              fillColor: Colors.white,
              hintText: 'Enter condition',
              hintStyle: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            onChanged: (value) => setState(() {}),
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildConditionPotentialityContent() {
    final controllers = _controllers['Condition Potentiality']!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Two dropdown fields in a row
        Row(
          children: [
            // Select Source Entity
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Source Entity',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select',
                    list: [
                      'Select',
                      'Customer',
                      'Order',
                      'Product',
                      'Address',
                      'Payment',
                      'Invoice',
                      'User',
                      'Account',
                      'Transaction',
                    ],
                    value: controllers['sourceEntity']!.text.isEmpty
                        ? 'Select'
                        : controllers['sourceEntity']!.text,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        controllers['sourceEntity']!.text =
                            selectedValue as String;
                        // Reset the attribute dropdown when entity changes
                        controllers['sourceEntityAttribute']!.text = '';
                      });
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Source Entity.Attribute
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Source Entity.Attribute',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select Entity.Attribute',
                    list:
                        _getEntityAttributes(controllers['sourceEntity']!.text),
                    value: controllers['sourceEntityAttribute']!.text.isEmpty
                        ? 'Select Entity.Attribute'
                        : controllers['sourceEntityAttribute']!.text,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        controllers['sourceEntityAttribute']!.text =
                            selectedValue as String;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 80), // Extra spacing to match the design
      ],
    );
  }

  /// Get attributes based on selected entity
  List<String> _getEntityAttributes(String entity) {
    switch (entity) {
      case 'Customer':
        return [
          'Select Entity.Attribute',
          'Customer.ID',
          'Customer.Name',
          'Customer.Email',
          'Customer.Phone',
          'Customer.Address',
          'Customer.DateOfBirth',
          'Customer.Status',
        ];
      case 'Order':
        return [
          'Select Entity.Attribute',
          'Order.ID',
          'Order.Date',
          'Order.Amount',
          'Order.Status',
          'Order.CustomerID',
          'Order.PaymentMethod',
        ];
      case 'Product':
        return [
          'Select Entity.Attribute',
          'Product.ID',
          'Product.Name',
          'Product.Price',
          'Product.Category',
          'Product.Stock',
          'Product.Description',
        ];
      case 'Address':
        return [
          'Select Entity.Attribute',
          'Address.Street',
          'Address.City',
          'Address.State',
          'Address.ZipCode',
          'Address.Country',
        ];
      case 'Payment':
        return [
          'Select Entity.Attribute',
          'Payment.ID',
          'Payment.Amount',
          'Payment.Method',
          'Payment.Status',
          'Payment.Date',
        ];
      case 'Invoice':
        return [
          'Select Entity.Attribute',
          'Invoice.ID',
          'Invoice.Number',
          'Invoice.Date',
          'Invoice.Amount',
          'Invoice.Status',
        ];
      case 'User':
        return [
          'Select Entity.Attribute',
          'User.ID',
          'User.Username',
          'User.Email',
          'User.Role',
          'User.LastLogin',
        ];
      case 'Account':
        return [
          'Select Entity.Attribute',
          'Account.ID',
          'Account.Number',
          'Account.Type',
          'Account.Balance',
          'Account.Status',
        ];
      case 'Transaction':
        return [
          'Select Entity.Attribute',
          'Transaction.ID',
          'Transaction.Amount',
          'Transaction.Type',
          'Transaction.Date',
          'Transaction.Status',
        ];
      default:
        return [
          'Select Entity.Attribute',
        ];
    }
  }

  Widget _buildCustomDropdownField(String label, String value,
      List<String> options, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        CustomDropdownWidget(
          label: value,
          list: options,
          value: options.contains(value) ? value : options.first,
          onChanged: (dynamic selectedValue) =>
              onChanged(selectedValue as String),
        ),
      ],
    );
  }
}
